package consts

const (
	ADVERTISER                = "ADVERTISER"
	CUSTOMER_ADMIN            = "CUSTOMER_ADMIN"
	CUSTOMER_OPERATOR         = "CUSTOMER_OPERATOR"
	AGENT                     = "AGENT"
	AGENT_SYSTEM_ACCOUNT      = "AGENT_SYSTEM_ACCOUNT"
	CHILD_AGENT               = "CHILD_AGENT"
	PLATFORM_ROLE_STAR_AGENT  = "PLATFORM_ROLE_STAR_AGENT"
	PLATFORM_ROLE_STAR        = "PLATFORM_ROLE_STAR"
	PLATFORM_ROLE_STAR_ISV    = "PLATFORM_ROLE_STAR_ISV"
	PLATFORM_ROLE_STAR_MCN    = "PLATFORM_ROLE_STAR_MCN"
	PLATFORM_ROLE_AWEME       = "PLATFORM_ROLE_AWEME"
	PLATFORM_ROLE_LOCAL_AGENT = "PLATFORM_ROLE_LOCAL_AGENT"
)

const (
	Organization = "纵横组织"
	Agent        = "方舟"
)

// 订阅服务类型
const (
	ReportAdvertiser = "report.advertiser.activeprogram" // 广告主报表实时数据
	ReportProject    = "report.project.activeprogram"    // 广告投放升级版项目报表实时数据
	ReportPromotion  = "report.promotion.activeprogram"  // 广告投放升级版广告报表实时数据
	StatusProject    = "status.project.realtime"         // 广告投放升级版项目状态实时推送
	StatusPromotion  = "status.promotion.realtime"       // 广告投放升级版广告状态实时推送
)

const Delimiter = "|"

const AdNotifyTitle = "广告智能托管通知"

var MetricsNameMapping = map[string]string{
	"stat_cost":                        "消耗",
	"click_cnt":                        "点击数",
	"cpc_platform":                     "平均点击单价",
	"ctr":                              "点击率",
	"show_cnt":                         "展示数",
	"balance":                          "账户余额",
	"convert_cnt":                      "转化数",
	"conversion_rate":                  "转化率",
	"conversion_cost":                  "平均转化成本",
	"deep_convert_cost":                "深度转化成本",
	"deep_convert_rate":                "深度转化率",
	"attribution_game_in_app_roi_1day": "付费ROI",
	"attribution_micro_game_0d_roi":    "小程序/小游戏当日广告变现ROI",
	"active":                           "激活数",
	"active_cost":                      "激活成本",
	"active_rate":                      "激活率",
	"active_register_cost":             "注册成本",
	"active_register_rate":             "注册率",
	"game_pay_count":                   "付费次数",
	"attribution_next_day_open_cnt":    "次留数",
	"attribution_next_day_open_cost":   "次留成本",
	"attribution_next_day_open_rate":   "次留率",
	"game_addiction_rate":              "关键行为率",
	"game_addiction_cost":              "关键行为成本",
}

var ObjectTypeMapping = map[int]string{
	1: "账户",
	2: "项目",
	3: "广告",
}

var TimeScopeMapping = map[int]string{
	0: "当天",
	3: "过去三天",
	5: "过去五天",
	7: "过去七天",
}

const (
	AuditAccepted = "AUDIT_ACCEPTED" // 审核成功
	Auditing      = "AUDITING"       // 审核中
	AuditRejected = "AUDIT_REJECTED" // 审核失败
)

const RegionVersion = "2.3.2"

// 生成类型
type AdGenerateDataType string

const (
	DataTypeTotal   AdGenerateDataType = "TOTAL"   // 预估生成
	DataTypePreview AdGenerateDataType = "PREVIEW" // 生成预览
)

// 项目创建方式
type AdProjectCreateType string

const (
	NewProject   AdProjectCreateType = "NEW_PROJECT"   // 新建项目
	ExistProject AdProjectCreateType = "EXIST_PROJECT" // 已有项目
)

// 项目生成规则
type AdProjectRule string

const (
	Auto                 AdProjectRule = "AUTO"           // 按总广告数/每项目广告数
	ProjectMaterialGroup AdProjectRule = "MATERIAL_GROUP" // 根据创意组生成
	ProjectTitleGroup    AdProjectRule = "TITLE_GROUP"    // 根据标题包生成
	ProjectAssignNumber  AdProjectRule = "ASSIGN_NUMBER"  // 指定数量
)

// 广告生成规则
type AdPromotionRule string

const (
	PromotionMaterialGroup              AdPromotionRule = "MATERIAL_GROUP"                 // 按创意组数
	PromotionMaterialGroupAndTitleGroup AdPromotionRule = "MATERIAL_GROUP_AND_TITLE_GROUP" // 按创意组*标题包
	PromotionAssignNumber               AdPromotionRule = "ASSIGN_NUMBER"                  // 指定数量
)

// 创意素材分配方式
type CreateMaterialAllocationMethod string

const (
	CreateMaterialSame       CreateMaterialAllocationMethod = "SAME"       // 全账户复用
	CreateMaterialAverage    CreateMaterialAllocationMethod = "AVERAGE"    // 平均分配
	CreateMaterialAdvertiser CreateMaterialAllocationMethod = "ADVERTISER" // 分账户选择
)

// 标题包分配方式
type TitlePackageAllocationMethod string

const (
	TitlePackageSame       TitlePackageAllocationMethod = "SAME"       // 全账户复用
	TitlePackageAverage    TitlePackageAllocationMethod = "AVERAGE"    // 平均分配
	TitlePackageAdvertiser TitlePackageAllocationMethod = "ADVERTISER" // 分账户选择
)

// 标题来源
type TitleSource string

const (
	TitleSourceLibrary TitleSource = "LIBRARY" // 标题包
	TitleSourcePackage TitleSource = "PACKAGE" // 标题库
)

// 落地页分配方式
type LandingPageAllocationMethod string

const (
	LandingPageSame       LandingPageAllocationMethod = "SAME"       // 全部相同
	LandingPageAdvertiser LandingPageAllocationMethod = "ADVERTISER" // 按账户分配
	LandingPageProject    LandingPageAllocationMethod = "PROJECT"    // 按项目分配
	LandingPagePromotion  LandingPageAllocationMethod = "PROMOTION"  // 按广告分配
)

type LandingPageType string

const (
	LandingPageOrange  LandingPageType = "ORANGE"  // 橙子落地页
	LandingPageThird   LandingPageType = "THIRD"   // 第三方落地页
	LandingPageDynamic LandingPageType = "DYNAMIC" // 动态链接生成
	LandingPageTemple  LandingPageType = "Temple"  // 根据模板生成
	LandingPageManual  LandingPageType = "MANUAL"  // 手动填写
)

// 产品匹配方式
type ProductAllocationMethod string

const (
	ProductSame       ProductAllocationMethod = "SAME"       // 所有项目选择同一组产品
	ProductAdvertiser ProductAllocationMethod = "ADVERTISER" // 每个账户选择一组产品
	ProductProject    ProductAllocationMethod = "PROJECT"    // 每个项目选择一组产品
)

// 星广联投匹配方式 SAME ADVERTISER
type StarAllocationMethod string

const (
	StarSame       StarAllocationMethod = "SAME"       // 所有账户选择一个任务
	StarAdvertiser StarAllocationMethod = "ADVERTISER" // 每个账户选择一个任务
)

// 小程序匹配方式
type MicroAllocationMethod string

const (
	MicroAdvertiser MicroAllocationMethod = "ADVERTISER" // 每个账户选择一个小程序
	MicroProject    MicroAllocationMethod = "PROJECT"    // 每个项目选择一个小程序
)

// 定向包匹配方式
type AudienceAllocationMethod string

const (
	AudienceSame       AudienceAllocationMethod = "SAME"       // 所有账户选择一个定向包
	AudienceAdvertiser AudienceAllocationMethod = "ADVERTISER" // 每个账户选择一个定向包
	AudienceProject    AudienceAllocationMethod = "PROJECT"    // 每个项目选择一个定向包
)

// 原生锚点类型
type AnchorRelatedType string

const (
	AUTO_AnchorRelatedType   AnchorRelatedType = "AUTO"
	OFF_AnchorRelatedType    AnchorRelatedType = "OFF"
	SELECT_AnchorRelatedType AnchorRelatedType = "SELECT"
	// 模板生成
	TEMPLATE_AnchorRelatedType AnchorRelatedType = "TEMPLATE"
)

// 预算出价配置模式
type BidAllocationMethod string

const (
	BidSame       BidAllocationMethod = "SAME"       // 所有账户选择一个出价
	BidAdvertiser BidAllocationMethod = "ADVERTISER" // 每个账户选择一个出价
	BidProject    BidAllocationMethod = "PROJECT"    // 每个项目选择一个出价
)

type BudgetMode string

const (
	BudgetModeDay      BudgetMode = "BUDGET_MODE_DAY"
	BudgetModeInfinite BudgetMode = "BUDGET_MODE_INFINITE"
	BudgetModeTotal    BudgetMode = "BUDGET_MODE_TOTAL"
)

// TrackUrlAllocationMethod 监测链接匹配方式
type TrackUrlAllocationMethod string

const (
	TrackUrlSame       TrackUrlAllocationMethod = "SAME"       // 所有项目选择同一个监测链接
	TrackUrlAdvertiser TrackUrlAllocationMethod = "ADVERTISER" // 每个账户选择一个监测链接
	TrackUrlProject    TrackUrlAllocationMethod = "PROJECT"    // 每个项目选择一个监测链接
)

// 推广身份匹配方式
type AwemeAllocationMethod string

const (
	AwemeSame       AwemeAllocationMethod = "SAME"       // 所有广告选择同一个抖音号
	AwemeAdvertiser AwemeAllocationMethod = "ADVERTISER" // 每个账户选择一个抖音号
	AwemeProject    AwemeAllocationMethod = "PROJECT"    // 每个项目选择一个抖音号
	AwemePromotion  AwemeAllocationMethod = "PROMOTION"  // 每个广告选择一个抖音号
)

type PromoteIdentity string

// AWEME Account
const (
	PromoteIdentityAccount PromoteIdentity = "ACCOUNT"
	PromoteIdentityAWEME   PromoteIdentity = "AWEME"
)

// 抖音号来源 HAND CREATIVE
type AwemeSource string

const (
	AwemeSourceManual   AwemeSource = "MANUAL"
	AwemeSourceCreative AwemeSource = "CREATIVE"
)

// 原生锚点匹配方式
type AnchorAllocationMethod string

const (
	AnchorAdvertiser AnchorAllocationMethod = "ADVERTISER" // 每个账户选择一个锚点
	AnchorProject    AnchorAllocationMethod = "PROJECT"    // 每个项目选择一个锚点
	AnchorPromotion  AnchorAllocationMethod = "PROMOTION"  // 每个广告选择一个锚点
)

// 字节小程序链接匹配方式
type MiniProgramLinkAllocationMethod string

const (
	MiniProgramLinkSame       MiniProgramLinkAllocationMethod = "SAME"       // 所有账户选择一个链接
	MiniProgramLinkAdvertiser MiniProgramLinkAllocationMethod = "ADVERTISER" // 每个账户选择一个链接
	MiniProgramLinkProject    MiniProgramLinkAllocationMethod = "PROJECT"    // 每个项目选择一个链接
	MiniProgramLinkPromotion  MiniProgramLinkAllocationMethod = "PROMOTION"  // 每个广告选择一个链接
)

// 创意组件匹配方式
type CreativeComponentAllocationMethod string

const (
	CreativeComponentAdvertiser CreativeComponentAllocationMethod = "ADVERTISER" // 每个账户选择一个创意组件
	CreativeComponentProject    CreativeComponentAllocationMethod = "PROJECT"    // 每个项目选择一个创意组件
	CreativeComponentPromotion  CreativeComponentAllocationMethod = "PROMOTION"  // 每个广告选择一个创意组件
)

// 广告设置匹配方式
type PromotionAllocationMethod string

const (
	PromotionSame       PromotionAllocationMethod = "SAME"       // 统一配置
	PromotionAdvertiser PromotionAllocationMethod = "ADVERTISER" // 分账户配置
	PromotionProject    PromotionAllocationMethod = "PROJECT"    // 分项目配置
)

// MicroPromotionType 小程序类型
type MicroPromotionType string

const (
	//WECHAT_GAME 微信小游戏
	MicroPromotionTypeWECHAT_GAME MicroPromotionType = "WECHAT_GAME"
	//WECHAT_APP微信小程序
	MicroPromotionTypeWECHAT_APP MicroPromotionType = "WECHAT_APP"
	//BYTE_GAME字节小游戏
	MicroPromotionTypeBYTE_GAME MicroPromotionType = "BYTE_GAME"
	//BYTE_APP字节小程序
	MicroPromotionTypeBYTE_APP MicroPromotionType = "BYTE_APP"
	//AWEME抖音号
	MicroPromotionTypeAWEME MicroPromotionType = "AWEME"
)

// 快手订单类型 本账号售卖、子账户售卖、分销售卖
const (
	AccountSale      = "本账号售卖"
	SubAccountSale   = "子账号售卖"
	DistributionSale = "分销售卖"
)

type KsAdGroupRuleType string

// 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE
const (
	KsAdGroupRuleTypeAudiencePackage KsAdGroupRuleType = "AUDIENCE_PACKAGE"
	KsAdGroupRuleTypeCreative        KsAdGroupRuleType = "CREATIVE"
	KsAdGroupRuleTypeTitle           KsAdGroupRuleType = "TITLE"
)

type KsTitleAllocationMethod string

const (
	KsTitleAllocationMethodAuto KsTitleAllocationMethod = "AUTO"
	KsTitleAllocationMethodTest KsTitleAllocationMethod = "TEST"
)

type KsShortPlayAllocationMethod string

// SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
const (
	KsShortPlayAllocationMethodSame       KsShortPlayAllocationMethod = "SAME"
	KsShortPlayAllocationMethodAdvertiser KsShortPlayAllocationMethod = "ADVERTISER"
	KsShortPlayAllocationMethodCampaign   KsShortPlayAllocationMethod = "CAMPAIGN"
	KsShortPlayAllocationMethodUnit       KsShortPlayAllocationMethod = "UNIT"
)

type KsProductAllocationMethod string

// 产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
const (
	KsProductAllocationMethodSame       KsProductAllocationMethod = "SAME"
	KsProductAllocationMethodAdvertiser KsProductAllocationMethod = "ADVERTISER"
	KsProductAllocationMethodCampaign   KsProductAllocationMethod = "CAMPAIGN"
	KsProductAllocationMethodUnit       KsProductAllocationMethod = "UNIT"
)

type KsBidAllocationMethod string

// 出价分配规则 全部相同：SAME 按账户分配：ADVERTISER
const (
	KsBidAllocationMethodSame       KsBidAllocationMethod = "SAME"
	KsBidAllocationMethodAdvertiser KsBidAllocationMethod = "ADVERTISER"
)

type KsDescriptionMatchMethod string

// 文案匹配方式 MANUAL CREATIVE GROUP
const (
	KsDescriptionMatchMethodManual   KsDescriptionMatchMethod = "MANUAL"
	KsDescriptionMatchMethodCreative KsDescriptionMatchMethod = "CREATIVE"
	KsDescriptionMatchMethodGroup    KsDescriptionMatchMethod = "GROUP"
)

type KsAccountBatchRule string

const (
	KsAccountBatchRuleOnDemand KsAccountBatchRule = "ON_DEMAND"
)

//	========================== 批量任务开始 ==========================
//
// 媒体类型
const (
	MediaOcean = iota + 1
	MediaTencent
	MediaKs
)

var MediaTypeMapping = map[int]string{
	MediaOcean:   "巨量",
	MediaTencent: "广点通",
	MediaKs:      "快手",
}

// 操作对象类型
const (
	OptObjectAdvertiser = iota + 1
	OptObjectProject
	OptObjectPromotion
	OptObjectCampaign
	OptObjectUnit
)

var OptObjectMapping = map[int]string{
	OptObjectAdvertiser: "账户",
	OptObjectProject:    "项目",
	OptObjectPromotion:  "广告",
	OptObjectCampaign:   "广告计划",
	OptObjectUnit:       "广告组",
}

// 操作类型
const (
	OptTypeEditAdvertiserName = iota + 1
	OptTypeEditAdvertiserRemark
	OptTypeEditAdvertiserAvatar
	OptTypeEditAdvertiserBudget
	OptTypeEditAdvertiserAutoInfo
	OptTypeEditAdvertiserIncExplore
	OptTypePlacement
	OptTypePause
	OptTypeDelete
	OptTypeDayBudget
	OptTypeEditRoiRatio
	OptTypeEditUnitName
	OptTypeEditDeliveryPeriod
)

var OptTypeMapping = map[int]string{
	OptTypeEditAdvertiserName:       "修改账户名称",
	OptTypeEditAdvertiserRemark:     "修改账户备注",
	OptTypeEditAdvertiserAvatar:     "修改账户头像",
	OptTypeEditAdvertiserBudget:     "修改账户预算",
	OptTypeEditAdvertiserAutoInfo:   "修改账户智投",
	OptTypeEditAdvertiserIncExplore: "修改增量探索",
	OptTypePlacement:                "投放",
	OptTypePause:                    "暂停",
	OptTypeDelete:                   "删除",
	OptTypeDayBudget:                "修改日预算",
	OptTypeEditRoiRatio:             "修改ROI系数",
	OptTypeEditUnitName:             "修改广告组名称",
	OptTypeEditDeliveryPeriod:       "修改投放时段",
}

// 操作状态
const (
	OptStatusCompleted = "COMPLETED"
	OptStatusExecuting = "EXECUTING"
)

var OptStatusMapping = map[string]string{
	OptStatusCompleted: "完成",
	OptStatusExecuting: "执行中",
}

// 执行结果
const (
	OptResultSuccess = "SUCCESS"
	OptResultFail    = "FAIL"
)

var OptResultMapping = map[string]string{
	OptResultSuccess: "成功",
	OptResultFail:    "失败",
}

const (
	ErrMsgGetAccessToken       = "获取access_token失败"
	ErrMsgEditAdvertiserName   = "非代理商不支持修改账户名称"
	ErrMsgEditAdvertiserRemark = "非代理商不支持修改账户备注"
	ErrMsgAdvertiserIsEmpty    = "媒体账户为空"
	ErrMsgUnSupportOptType     = "不支持的操作类型"
)

const (
	PushMethodUnified = iota + 1
	PushMethodSeparate
)

var PushMethodMapping = map[int]string{
	PushMethodUnified:  "统一推送",
	PushMethodSeparate: "分别推送",
}

const (
	PushTypeAdvertiser = iota + 1
	PushTypeExistApplet
)

var PushTypeMapping = map[int]string{
	PushTypeAdvertiser:  "账户",
	PushTypeExistApplet: "已有小程序资产",
}

//	========================== 批量任务结束 ==========================

const (
	ManSenPlatform    = 1
	FanQiePlatform    = 2
	DianZhongPlatform = 3
)

const (
	ManSenPlatformStr    = "漫森"
	FanQiePlatformStr    = "番茄"
	DianZhongPlatformStr = "点众"
)

const (
	GetAdvertiserPublicInfoErrMsg  = "No permission to operate account "
	GetAdvertiserPublicInfoErrMsg2 = " doesn't exist or the role is wrong"
	GetAdvertiserPublicInfoErrMsg3 = "Account "
	TooManyRequestErrMsg           = "Too many requests"
	RateLimitRetryNeeded           = "RATE_LIMIT_RETRY_NEEDED"
)
