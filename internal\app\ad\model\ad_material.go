// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-12 16:58:16
// 生成路径: internal/app/ad/model/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialInfoRes is the golang structure for table ad_material.
type AdMaterialInfoRes struct {
	gmeta.Meta   `orm:"table:ad_material"`
	MaterialId   int    `orm:"material_id,primary" json:"materialId" dc:"素材id"`                       // 素材id
	AlbumId      int    `orm:"album_id" json:"albumId" dc:"专辑id"`                                     // 专辑id
	FileId       int    `orm:"file_id" json:"fileId" dc:"文件夹id"`                                      // 文件夹id
	MaterialName string `orm:"material_name" json:"materialName" dc:"素材名"`                            // 素材名
	MaterialType string `orm:"material_type" json:"materialType" dc:"material_type 素材类型 video image"` // material_type 素材类型 video image
	UserId       int    `orm:"user_id" json:"userId" dc:"创建人"`                                        // 创建人
	DesignUserId int    `orm:"design_user_id" json:"designUserId" dc:"设计师"`                           // 设计师
	FileUri      string `orm:"file_uri" json:"fileUri" dc:"文件url地址"`                                  // 文件url地址
	ThumbnailUri string `orm:"thumbnail_uri" json:"thumbnailUri" dc:"缩略图url / 封面图 "`                  // 缩略图url / 封面图
	Labels       string `orm:"labels" json:"labels" dc:"字符串数组以| 分隔"`                                  // 字符串数组以| 分隔
	FileFormat   string `orm:"file_format" json:"fileFormat" dc:"文件格式 MP4 JPG 等"`                     // 文件格式 MP4 JPG 等
	FileSize     string `orm:"file_size" json:"fileSize" dc:"文件大小 eg: 413.87M"`                       // 文件大小 eg: 413.87M
	Width        int    `orm:"width" json:"width" dc:"尺寸宽"`                                           // 尺寸宽
	Height       int    `orm:"height" json:"height" dc:"尺寸高"`                                         // 尺寸高
	// 文件夹目录
	FilePath string `orm:"file_path" json:"filePath" dc:"文件目录"`
	// 设计师
	DesignUser           string      `orm:"design_user" json:"designUser"`
	Remark               string      `orm:"remark" json:"remark" dc:"备注"`                  // 备注
	ManageStatus         int         `orm:"manage_status" json:"manageStatus"`             // 素材管理状态
	VideoDuration        int         `orm:"video_duration" json:"videoDuration" dc:"视频时长"` // 视频时长
	MediaId              string      `orm:"media_id" json:"mediaId" dc:"图片/视频ID"`          // 图片/视频ID
	VideoThumbnailId     string      `orm:"video_thumbnail_id" json:"videoThumbnailId"`    // 视频缩略图id
	CreativeMaterialType int         `json:"creative_material_type,omitempty"`
	TaskId               int64       `orm:"task_id" json:"taskId"`
	CreatedAt            *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	AlbumName            string      `orm:"album_name" json:"albumName" dc:"专辑名称"`
	ErrorMsg             string      `json:"errorMsg" dc:"错误信息"`
	JsonStr              string      `json:"jsonStr"`
	//Description
	Description string `orm:"description" json:"description" dc:"广告语"`
}

type AdMaterialListRes struct {
	MaterialId    int         `json:"materialId" dc:"素材id"`
	AlbumId       int         `json:"albumId" dc:"专辑id"`
	FileId        int         `json:"fileId" dc:"文件夹id"`
	MaterialName  string      `json:"materialName" dc:"素材名"`
	MaterialType  string      `json:"materialType" dc:"material_type 素材类型 video image"`
	UserId        int         `json:"userId" dc:"创建人"`
	DesignUserId  int         `json:"designUserId" dc:"设计师"` // 设计师
	FileUri       string      `json:"fileUri" dc:"文件url地址"`
	ThumbnailUri  string      `json:"thumbnailUri" dc:"缩略图url / 封面图 "`
	Labels        string      `json:"labels" dc:"字符串数组以| 分隔"`
	FileFormat    string      `json:"fileFormat" dc:"文件格式 MP4 JPG 等"`
	FileSize      string      `json:"fileSize" dc:"文件大小 eg: 413.87M"`
	ManageStatus  int         `orm:"manage_status" json:"manageStatus"` // 素材管理状态
	Width         int         `json:"width" dc:"尺寸宽"`
	Height        int         `json:"height" dc:"尺寸高"`
	Remark        string      `json:"remark" dc:"备注"`
	VideoDuration int         `json:"videoDuration" dc:"视频时长"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
}

type SpecialProductListReq struct {
	comModel.PageReq
	KeyWord     string `p:"keyWord" dc:"关键字"`
	KeywordType int    `p:"keywordType"`        // 1 全部  2 专辑 3 文件夹 配合关键字一起使用
	AlbumId     int    `p:"albumId"  dc:"专辑id"` //专辑id   有专辑id 查的是文件夹
	FileId      int    `p:"fileId"  dc:"文件夹id"` //专辑id
}

// SpecialProductListRes 列表返回结果
type SpecialProductListRes struct {
	comModel.ListRes
	List []*SpecialProduct `json:"list"`
}

type SpecialProductAlbum struct {
	AlbumID      int      `json:"album_id"`       // 专辑id
	AlbumName    string   `json:"album_name"`     // 专辑名
	Remark       string   `json:"remark"`         // 备注
	CreateTime   string   `json:"create_time"`    // 创建十几件
	CreateUserID int      `json:"create_user_id"` // 创建用户id
	MaterialNum  int      `json:"material_num"`   // 素材数量
	Preview      []string `json:"preview"`        // 缩略图
}

type SpecialProductFile struct {
	FileID       int    `json:"file_id"`
	FileName     string `json:"file_name"`
	AlbumID      int    `json:"album_id"` // 专辑id
	Remark       string `json:"remark"`   // 备注
	CreateTime   string `json:"create_time"`
	CreateUserID int    `json:"create_user_id"` // 创建用户id
	ParentID     int    `json:"parent_id"`
	//RelationMaterialGroupID string   `json:"relation_material_group_id"`
	MaterialNum  int      `json:"material_num"`  // 素材数量
	ManageStatus int      `json:"manage_status"` // 素材管理状态 0 未管理 1 已管理
	Preview      []string `json:"preview"`       // 缩略图
}
type SpecialProductMaterial struct {
	AlbumID       int         `json:"albumId"` // 专辑id
	FileID        int         `json:"fileId"`
	MaterialID    int         `json:"materialId"`   // 素材id
	MaterialName  string      `json:"materialName"` // 素材名称
	AspectRatio   float64     `json:"aspectRatio"`  // 宽高比
	MaterialType  string      `json:"materialType" dc:"material_type 素材类型 video image"`
	UserId        int         `json:"userId" dc:"创建人"`
	FileUri       string      `json:"fileUri" dc:"文件url地址"`
	ThumbnailUri  string      `json:"thumbnailUri" dc:"缩略图url / 封面图 "`
	Labels        string      `json:"labels" dc:"字符串数组以| 分隔"`
	FileFormat    string      `json:"fileFormat" dc:"文件格式 MP4 JPG 等"`
	FileSize      string      `json:"fileSize" dc:"文件大小 eg: 413.87M"`
	Width         int         `json:"width" dc:"尺寸宽"`
	Height        int         `json:"height" dc:"尺寸高"`
	Remark        string      `json:"remark" dc:"备注"`
	VideoDuration int         `json:"videoDuration" dc:"视频时长"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// SpecialProduct 文件夹视图
type SpecialProduct struct {
	Type     int                    `json:"type"` // 1 专辑 2 为文件夹  3 为素材文件
	Album    SpecialProductAlbum    `json:"album"`
	File     SpecialProductFile     `json:"file"`
	Material SpecialProductMaterial `json:"material"`
}

// AdMaterialSearchReq 分页请求参数
type AdMaterialSearchReq struct {
	comModel.PageReq
	MaterialId    string `p:"materialId" dc:"素材id"`                                                   //素材id
	AlbumId       int    `p:"albumId" v:"albumId@integer#专辑id需为整数" dc:"专辑id"`                         //专辑id
	FileId        int    `p:"fileId" v:"fileId@integer#文件夹id需为整数" dc:"文件夹id"`                         //文件夹id
	MaterialName  string `p:"materialName" dc:"素材名"`                                                  //素材名
	MaterialType  string `p:"materialType" dc:"material_type 素材类型 video image"`                       //material_type 素材类型 video image
	UserId        string `p:"userId" v:"userId@integer#创建人需为整数" dc:"创建人"`                             //创建人
	FileUri       string `p:"fileUri" dc:"文件url地址"`                                                   //文件url地址
	ThumbnailUri  string `p:"thumbnailUri" dc:"缩略图url / 封面图 "`                                        //缩略图url / 封面图
	Labels        string `p:"labels" dc:"字符串数组以| 分隔"`                                                 //字符串数组以| 分隔
	FileFormat    string `p:"fileFormat" dc:"文件格式 MP4 JPG 等"`                                         //文件格式 MP4 JPG 等
	FileSize      string `p:"fileSize" dc:"文件大小 eg: 413.87M"`                                         //文件大小 eg: 413.87M
	Width         string `p:"width" v:"width@integer#尺寸宽需为整数" dc:"尺寸宽"`                               //尺寸宽
	Height        string `p:"height" v:"height@integer#尺寸高需为整数" dc:"尺寸高"`                             //尺寸高
	VideoDuration string `p:"videoDuration" v:"videoDuration@integer#视频时长需为整数" dc:"视频时长"`             //视频时长
	ManageStatus  int    `orm:"manage_status" json:"manageStatus"`                                    // 素材管理状态
	CreatedAt     string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	StartTime     string `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime       string `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
}

// AdMaterialSearchRes 列表返回结果
type AdMaterialSearchRes struct {
	comModel.ListRes
	List []*AdMaterialListRes `json:"list"`
}

// AdMaterialAddReq 添加操作请求参数
type AdMaterialAddReq struct {
	//MaterialId    int    `p:"materialId" v:"required#主键ID不能为空" dc:"素材id"`
	AlbumId       int    `p:"albumId"  dc:"专辑id"`
	FileId        int    `p:"fileId"  dc:"文件夹id"`
	MaterialName  string `p:"materialName" v:"required#素材名不能为空" dc:"素材名"`
	MaterialType  string `p:"materialType"  dc:"material_type 素材类型 video image"`
	UserId        int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	DesignUserId  int    `p:"designUserId" dc:"设计师"` // 设计师
	FileUri       string `p:"fileUri"  dc:"文件url地址"`
	ThumbnailUri  string `p:"thumbnailUri"  dc:"缩略图url / 封面图 "`
	Labels        string `p:"labels"  dc:"字符串数组以| 分隔"`
	FileFormat    string `p:"fileFormat"  dc:"文件格式 MP4 JPG 等"`
	FileSize      string `p:"fileSize"  dc:"文件大小 eg: 413.87M"`
	Width         int    `p:"width"  dc:"尺寸宽"`
	Height        int    `p:"height"  dc:"尺寸高"`
	AdType        int    `p:"adType" dc:"广告类型 1:巨量  2 快手"`
	ManageStatus  int    `orm:"manage_status" json:"manageStatus"` // 素材管理状态
	Remark        string `p:"remark"  dc:"备注"`
	VideoDuration int    `p:"videoDuration"  dc:"视频时长"`
}

// AdMaterialEditReq 修改操作请求参数
type AdMaterialEditReq struct {
	MaterialId    int    `p:"materialId" v:"required#主键ID不能为空" dc:"素材id"`
	AlbumId       int    `p:"albumId"  dc:"专辑id"`
	FileId        int    `p:"fileId"  dc:"文件夹id"`
	MaterialName  string `p:"materialName" v:"required#素材名不能为空" dc:"素材名"`
	MaterialType  string `p:"materialType"  dc:"material_type 素材类型 video image"`
	UserId        int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	DesignUserId  int    `p:"designUserId" dc:"设计师"` // 设计师
	FileUri       string `p:"fileUri"  dc:"文件url地址"`
	ThumbnailUri  string `p:"thumbnailUri"  dc:"缩略图url / 封面图 "`
	Labels        string `p:"labels"  dc:"字符串数组以| 分隔"`
	FileFormat    string `p:"fileFormat"  dc:"文件格式 MP4 JPG 等"`
	FileSize      string `p:"fileSize"  dc:"文件大小 eg: 413.87M"`
	ManageStatus  int    `orm:"manage_status" json:"manageStatus"` // 素材管理状态
	Width         int    `p:"width"  dc:"尺寸宽"`
	Height        int    `p:"height"  dc:"尺寸高"`
	Remark        string `p:"remark"  dc:"备注"`
	VideoDuration int    `p:"videoDuration"  dc:"视频时长"`
}

type DelMaterial struct {
	FileId       int    `p:"fileId"  dc:"文件夹id"`
	Count        int    `p:"count"  dc:"删除数量"`
	ThumbnailUri string `p:"thumbnailUri"  dc:"缩略图url / 封面图 "`
}
