package generate

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

// 广告分组规则：按创意分组

type GroupByCreative struct{}

func (s *GroupByCreative) GetEstimateInfo(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq) (res map[int64]*model.EstimateInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.KsAdvertiserStrategyMaterialConfig.CreativeMaterialData) < len(req.KsAdvertiserStrategyRuleReq.AdvertiserIds) {
			liberr.ErrIsNil(ctx, errors.New("按创意分组时，创意组数量需大于等于账户数"))
		}
		advertiserInfos, _ := service.KsAdvertiserAccountInfo().GetByAccountIds(ctx, req.KsAdvertiserStrategyRuleReq.AdvertiserIds)
		// 分配创意素材
		creativeMaterialMap := AllocationCreativeMaterial(req)
		// 分配文案
		titleMap := AllocationTitle(req)
		res = make(map[int64]*model.EstimateInfo)
		for _, v := range advertiserInfos {
			creativeMaterials := creativeMaterialMap[v.AccountId]
			titles := titleMap[v.AccountId]
			unitNum := CalculateUnitNum(creativeMaterials, titles, req.KsAdvertiserStrategyTitleConfig.TitleAllocationMethod)
			campaignNum := CalculateCampaignNum(unitNum, req.KsAdvertiserStrategyCampaignConfig.AdUnitLimit)
			// 构建预估信息
			estimateInfo := &model.EstimateInfo{
				AdvertiserId:         v.AccountId,
				AdvertiserName:       v.AccountName,
				AdvertiserRemark:     v.Remark,
				CampaignNum:          campaignNum,
				UnitNum:              unitNum,
				CreativeMaterialList: creativeMaterials,
				TitleList:            titles,
			}
			res[v.AccountId] = estimateInfo
		}
	})
	return
}

func (s *GroupByCreative) GeneratePreviewBaseInfo(
	ctx context.Context,
	req *model.KsAdvertiserStrategyGenerateReq,
	estimateInfoMap map[int64]*model.EstimateInfo) (res []*model.PreviewBaseInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.PreviewBaseInfo, 0)
		uniqueId := libUtils.GenerateID()
		for _, estimateInfo := range estimateInfoMap {
			previewBaseInfo := &model.PreviewBaseInfo{
				AdvertiserId:   estimateInfo.AdvertiserId,
				AdvertiserName: estimateInfo.AdvertiserName,
			}
			unitList := BuildPreviewUnitListByMaterialAndTitle(estimateInfo, req, uniqueId)
			previewBaseInfo.CampaignList = AllocationUnitAverage(unitList, estimateInfo.CampaignNum)
			res = append(res, previewBaseInfo)
		}
	})
	return
}
